<!DOCTYPE html>
<html lang="en">

<head>
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
  <link rel="manifest" href="/site.webmanifest" />
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Employment Background Verification Services - Verifycheck</title>
  <meta name="description"
    content="Professional employment background verification services by Verifycheck. Comprehensive employee screening, background checks, and verification solutions for businesses." />
  <meta name="author" content="Verifycheck" />

  <meta property="og:title" content="Employment Background Verification Services - Verifycheck" />
  <meta property="og:description"
    content="Professional employment background verification services by Verifycheck. Comprehensive employee screening, background checks, and verification solutions for businesses." />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="https://verifycheck.dev/opengraph-image.png" />

  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:site" content="@verifycheck_dev" />
  <meta name="twitter:image" content="https://verifycheck.dev/opengraph-image.png" />

  <!-- Google Tag Manager -->
  <script>
    (function (w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l != "dataLayer" ? "&l=" + l : "";
      j.async = true;
      j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
      f.parentNode.insertBefore(j, f);
    })(window, document, "script", "dataLayer", "GTM-NV86HSCC");
  </script>
  <!-- End Google Tag Manager -->

  <!-- Google Ads Global site tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=AW-11262958681"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag("js", new Date());
    gtag("config", "AW-11262958681");
  </script>
  <!-- End Google Ads Tag -->

  <!-- Meta Pixel Code -->
  <script>
    !function (f, b, e, v, n, t, s) {
      if (f.fbq) return; n = f.fbq = function () {
        n.callMethod ?
          n.callMethod.apply(n, arguments) : n.queue.push(arguments)
      };
      if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
      n.queue = []; t = b.createElement(e); t.async = !0;
      t.src = v; s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s)
    }(window, document, 'script',
      'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '2211966842596235');
    fbq('track', 'PageView');
  </script>
  <noscript><img height="1" width="1" style="display:none"
      src="https://www.facebook.com/tr?id=2211966842596235&ev=PageView&noscript=1" /></noscript>
  <!-- End Meta Pixel Code -->
</head>

<body>
  <!-- Google Tag Manager (noscript) -->
  <noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NV86HSCC" height="0" width="0"
      style="display: none; visibility: hidden">
    </iframe>
  </noscript>
  <!-- End Google Tag Manager (noscript) -->

  <!-- Meta Pixel (noscript) -->
  <noscript>
    <img height="1" width="1" style="display:none"
      src="https://www.facebook.com/tr?id=4042793616038735&ev=PageView&noscript=1" />
  </noscript>
  <!-- End Meta Pixel (noscript) -->

  <div id="root"></div>

  <!-- IMPOR TANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
  <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>